/**
 * Экспорты для системы маппинга текстур
 */

// Основные классы
export { TextureMapper, textureMapper } from './TextureMapper';
export { TextureLoader, textureLoader } from './TextureLoader';

// Типы
export type {
  TexturePathOptions,
  TextureConfig,
  SubtypeTextureConfig,
  MaterialTextureConfig,
  FallbackTexture,
  TextureLoadResult,
  TextureCache,
  TextureStats,
  PreloadConfig,
  FallbackOptions
} from './types';

// Конфигурации
export { textureConfigs } from './configs/textureConfigs';

// Утилиты
export {
  createFallbackTexture,
  createColoredFallback,
  createTextFallback,
  isValidTexture,
  createTransparentFallback,
  createGradientFallback,
  createCheckerboardFallback,
  getCachedFallback,
  clearFallbackCache
} from './utils/fallbackUtils';

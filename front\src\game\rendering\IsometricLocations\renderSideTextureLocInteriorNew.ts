/**
 * Новая система отрисовки текстур декораций с поддержкой decorationSides
 * Использует новую систему маппинга текстур
 */

import { LocationDecorations, MaterialTexture } from '../../../shared/enums';
import { Location, PointWithNumber } from '../../../shared/types/Location';
import { textureLoader, TexturePathOptions } from './textureMapping';
import { DECORATION_TEXTURE_SETTINGS } from '../../utils/constants/renderingLocation';
import { DECORATION_FADE } from '../../utils/constants/timeLight';
import { extractSideNumbers, buildComboKey, getDeterministicVariation } from './utils';

// Типы декораций, к которым применима эта логика (удобно расширять)
const SUPPORTED_SIDE_DECORATIONS: LocationDecorations[] = [
  LocationDecorations.WALL,
  LocationDecorations.WINDOW,
  LocationDecorations.DOOR,
  LocationDecorations.FENCE,
  LocationDecorations.MOUNTAINWALL,
  LocationDecorations.PARTITION
];

/**
 * Загружает текстуры для decorationSides с новой системой
 */
export async function loadSideTexturesNew(
  decoration: LocationDecorations,
  decorationSides: PointWithNumber,
  locationData?: Location,
  material?: MaterialTexture
): Promise<HTMLImageElement[]> {
  // Поддерживаем несколько типов (wall, window, door и др.)
  if (!SUPPORTED_SIDE_DECORATIONS.includes(decoration)) {
    return [];
  }

  // Извлекаем координаты и номера сторон
  const coordinates = { x: decorationSides[0], y: decorationSides[1] };
  const sideNumbers = decorationSides[2];

  if (!sideNumbers || sideNumbers.length === 0) {
    return [];
  }

  // Определяем состояние открыто/закрыто для дверей
  let isOpen: boolean | undefined;
  if (decoration === LocationDecorations.DOOR) {
    // Пробуем найти флаг isOpen в разных местах структуры interactive
    isOpen = Boolean(
      (locationData as any)?.interactive?.type?.isOpen ?? 
      (locationData as any)?.interactive?.isOpen ?? 
      false
    );
  }

  // Создаем опции для загрузки
  const options: TexturePathOptions = {
    decoration,
    location: locationData,
    material: material || locationData?.textureMaterial,
    decorationSides: decorationSides,
    coordinates,
    isOpen
  };

  try {
    const result = await textureLoader.loadSideTexture(decoration, decorationSides, locationData, options);
    return [result.image];
  } catch (error) {
    console.warn(`Не удалось загрузить side текстуру для ${decoration}:`, error);
    return [];
  }
}

/**
 * Синхронно получает загруженные текстуры для decorationSides
 */
export function getLoadedSideTexturesNew(
  decoration: LocationDecorations,
  decorationSides: PointWithNumber,
  locationData?: Location,
  material?: MaterialTexture
): HTMLImageElement[] {
  // Поддерживаем несколько типов (wall, window, door и др.)
  if (!SUPPORTED_SIDE_DECORATIONS.includes(decoration)) {
    return [];
  }

  // Извлекаем координаты и номера сторон
  const coordinates = { x: decorationSides[0], y: decorationSides[1] };
  const sideNumbers = decorationSides[2];

  if (!sideNumbers || sideNumbers.length === 0) {
    return [];
  }

  // Определяем состояние открыто/закрыто для дверей
  let isOpen: boolean | undefined;
  if (decoration === LocationDecorations.DOOR) {
    isOpen = Boolean(
      (locationData as any)?.interactive?.type?.isOpen ?? 
      (locationData as any)?.interactive?.isOpen ?? 
      false
    );
  }

  // Создаем опции для получения текстуры
  const options: TexturePathOptions = {
    decoration,
    location: locationData,
    material: material || locationData?.textureMaterial,
    decorationSides: decorationSides,
    coordinates,
    isOpen
  };

  const texture = textureLoader.getLoadedTexture(options);
  return texture ? [texture] : [];
}

/**
 * Отрисовывает текстуры decorationSides на тайле с новой системой
 */
export function drawSideTexturesNew(
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  decoration: LocationDecorations,
  decorationSides: PointWithNumber,
  locationData?: Location,
  material?: MaterialTexture,
  darknessLevel: number = 0,
  playerPosition: { x: number; y: number } | null = null,
  tilePos: { x: number; y: number } | null = null
): void {
  // Получаем загруженные текстуры
  const loaded = getLoadedSideTexturesNew(decoration, decorationSides, locationData, material);

  if (loaded.length === 0) {
    // Асинхронно загружаем текстуры
    loadSideTexturesNew(decoration, decorationSides, locationData, material);
    return;
  }

  const texture = loaded[0];
  if (!texture || !texture.complete) {
    return;
  }

  // Применяем настройки размера
  const scaleMultiplier = DECORATION_TEXTURE_SETTINGS.SCALE_MULTIPLIERS[decoration.toUpperCase() as keyof typeof DECORATION_TEXTURE_SETTINGS.SCALE_MULTIPLIERS] || 1;
  const drawW = DECORATION_TEXTURE_SETTINGS.DEFAULT_WIDTH * scaleMultiplier;
  const drawH = DECORATION_TEXTURE_SETTINGS.DEFAULT_HEIGHT * scaleMultiplier;

  ctx.save();

  // Применяем затемнение
  if (darknessLevel > 0) {
    const alpha = Math.max(0.1, 1 - darknessLevel);
    ctx.globalAlpha = alpha;
  }

  // Применяем fade эффект если игрок рядом
  if (playerPosition && tilePos) {
    const distance = Math.sqrt(
      Math.pow(playerPosition.x - tilePos.x, 2) + 
      Math.pow(playerPosition.y - tilePos.y, 2)
    );
    
    if (distance <= DECORATION_FADE.FADE_DISTANCE) {
      const fadeAlpha = Math.max(
        DECORATION_FADE.MIN_ALPHA,
        1 - (DECORATION_FADE.FADE_DISTANCE - distance) / DECORATION_FADE.FADE_DISTANCE
      );
      ctx.globalAlpha *= fadeAlpha;
    }
  }

  // Вычисляем смещение если включено
  let offsetX = 0;
  let offsetY = 0;
  if (DECORATION_TEXTURE_SETTINGS.ENABLE_OFFSET) {
    offsetX = DECORATION_TEXTURE_SETTINGS.HORIZONTAL_OFFSET.WALL || 0;
    offsetY = DECORATION_TEXTURE_SETTINGS.VERTICAL_OFFSET.WALL || 0;
  }

  ctx.drawImage(texture, centerX - drawW / 2 + offsetX, centerY - drawH / 2 + offsetY, drawW, drawH);
  ctx.restore();
}

/**
 * Предзагружает текстуры для всех возможных комбинаций decorationSides
 */
export async function preloadSideTexturesNew(): Promise<void> {
  const materials = [MaterialTexture.BETON, MaterialTexture.WOOD, MaterialTexture.METAL, MaterialTexture.BRICK];
  const sideNumbers = [1, 2, 3, 4]; // Возможные номера текстур

  // Генерируем все возможные комбинации
  const allCombinations: number[][] = [];
  
  // Одиночные стороны
  sideNumbers.forEach(side => {
    allCombinations.push([side]);
  });
  
  // Пары сторон
  for (let i = 0; i < sideNumbers.length; i++) {
    for (let j = i + 1; j < sideNumbers.length; j++) {
      allCombinations.push([sideNumbers[i], sideNumbers[j]]);
    }
  }
  
  // Тройки и четверки
  allCombinations.push([1, 2, 3]);
  allCombinations.push([1, 2, 4]);
  allCombinations.push([1, 3, 4]);
  allCombinations.push([2, 3, 4]);
  allCombinations.push([1, 2, 3, 4]);

  const loadPromises: Promise<any>[] = [];

  // Для каждого поддерживаемого типа создаём пути с материалами + комбинациями
  SUPPORTED_SIDE_DECORATIONS.forEach(decType => {
    const isDoor = decType === LocationDecorations.DOOR;
    
    materials.forEach(material => {
      allCombinations.forEach(combo => {
        const decorationSides: PointWithNumber = [0, 0, combo];
        
        // Обычные текстуры
        loadPromises.push(
          loadSideTexturesNew(decType, decorationSides, undefined, material).catch(() => {})
        );
        
        // Для дверей добавляем состояния открыто/закрыто
        if (isDoor) {
          // Создаем mock location с состоянием двери
          const openLocation = { interactive: { isOpen: true } } as any;
          const closedLocation = { interactive: { isOpen: false } } as any;
          
          loadPromises.push(
            loadSideTexturesNew(decType, decorationSides, openLocation, material).catch(() => {})
          );
          loadPromises.push(
            loadSideTexturesNew(decType, decorationSides, closedLocation, material).catch(() => {})
          );
        }
      });
    });
  });

  await Promise.allSettled(loadPromises);
  console.log('Предзагрузка side текстур завершена');
}

/**
 * Проверяет, поддерживает ли декорация decorationSides
 */
export function supportsSideTextures(decoration: LocationDecorations): boolean {
  return SUPPORTED_SIDE_DECORATIONS.includes(decoration);
}

# Миграция на новую систему маппинга текстур

Этот документ описывает, как перейти от старой системы рендеринга к новой.

## Что изменилось

### Старая система
- Хардкод путей к текстурам в коде
- Отдельные функции для каждого типа текстур
- Сложно добавлять новые варианты
- Нет централизованного управления

### Новая система
- Централизованный маппинг текстур
- Автоматическая поддержка subType и материалов
- Надежная fallback система
- Простое добавление новых текстур через конфигурацию

## Пошаговая миграция

### Шаг 1: Замена импортов

**Старый код:**
```typescript
import { drawLocationTile, drawLocationTileDecorations } from './renderUtilsLocInterior';
```

**Новый код:**
```typescript
import { drawLocationTileNew, drawLocationTileDecorationsNew } from './renderUtilsLocInteriorNew';
```

### Шаг 2: Замена функций рендеринга

**Старый код:**
```typescript
drawLocationTile(
  ctx, screenX, screenY, isoX, isoY, 
  tileWidth, tileHeight, canvasWidth, canvasHeight,
  cameraX, cameraY, location, cellTarget, 
  drawPlayer, drawDecorations, darknessLevel
);
```

**Новый код:**
```typescript
drawLocationTileNew(
  ctx, isoX, isoY, tileWidth, tileHeight,
  location, darknessLevel, fillOpacity
);
```

### Шаг 3: Замена загрузки текстур

**Старый код:**
```typescript
import { loadLocationTexture } from '../textures/locationTextures';

const texture = await loadLocationTexture('/textures/Location/decorations/wall/1.png');
```

**Новый код:**
```typescript
import { textureLoader } from './textureMapping';

const result = await textureLoader.loadDecorationTexture(
  LocationDecorations.WALL, 
  location
);
const texture = result.image;
```

### Шаг 4: Замена работы с decorationSides

**Старый код:**
```typescript
import { drawSideTextures } from './renderSideTextureLocInterior';

drawSideTextures(
  ctx, centerX, centerY, decoration, 
  decorationSides, locationData, material,
  darknessLevel, playerPosition, tilePos
);
```

**Новый код:**
```typescript
import { drawSideTexturesNew } from './renderSideTextureLocInteriorNew';

drawSideTexturesNew(
  ctx, centerX, centerY, decoration, 
  decorationSides, locationData, material,
  darknessLevel, playerPosition, tilePos
);
```

## Обновление файлов

### 1. drawEngineLocInterior.ts

Замените импорты:
```typescript
// Старое
import { drawLocationTile, drawLocationTileDecorations } from './renderUtilsLocInterior';

// Новое
import { drawLocationTileNew, drawLocationTileDecorationsNew } from './renderUtilsLocInteriorNew';
```

Замените вызовы функций в цикле рендеринга.

### 2. Другие файлы рендеринга

Найдите все места, где используются:
- `loadLocationTexture`
- `getLocationTexture`
- `drawLocationDecoration`
- `drawSideTextures`

И замените их на новые аналоги.

## Добавление новых текстур

### Простая декорация

1. **Добавьте enum:**
```typescript
// В shared/enums/index.ts
export enum LocationDecorations {
  // ... существующие
  MY_DECORATION = 'my_decoration'
}
```

2. **Добавьте в quickConfig.ts:**
```typescript
export const SIMPLE_DECORATIONS = {
  // ... существующие
  'my_decoration': 4  // 4 варианта
};
```

3. **Создайте файлы:**
```
/textures/Location/decorations/my_decoration/
├── 1.png
├── 2.png
├── 3.png
└── 4.png
```

### Декорация с материалами

1. **Добавьте в quickConfig.ts:**
```typescript
export const MATERIAL_DECORATIONS = {
  // ... существующие
  'my_wall': {
    variations: 4,
    materials: ['brick', 'wood'],
    supportsSides: true
  }
};
```

2. **Создайте структуру папок:**
```
/textures/Location/decorations/my_wall/
├── brick/
│   ├── 1.png
│   └── 2.png
└── wood/
    ├── 1.png
    └── 2.png
```

### Декорация для подтипа

1. **Добавьте в quickConfig.ts:**
```typescript
export const SUBTYPE_DECORATIONS = {
  // ... существующие
  'subway': {
    // ... существующие
    'my_decoration': 3
  }
};
```

2. **Создайте папку:**
```
/textures/Location/decorations/subway/my_decoration/
├── 1.png
├── 2.png
└── 3.png
```

## Тестирование

### 1. Проверка в браузере

Откройте консоль браузера и выполните:
```javascript
window.testNewTextureSystem()
```

### 2. Проверка fallback

Попробуйте загрузить несуществующую текстуру - должен появиться фиолетовый круг.

### 3. Проверка производительности

Сравните время загрузки до и после миграции:
```javascript
const stats = textureLoader.getLoadingStats();
console.log(stats);
```

## Откат изменений

Если что-то пошло не так, можно быстро откатиться:

1. Верните старые импорты
2. Используйте старые функции рендеринга
3. Удалите новые файлы

Старая система остается нетронутой и может использоваться параллельно.

## Преимущества новой системы

1. **Простота добавления текстур** - только конфигурация, без кода
2. **Автоматическая поддержка подтипов** - система сама выберет правильную текстуру
3. **Надежность** - fallback система предотвращает поломки
4. **Производительность** - умное кэширование и предзагрузка
5. **Масштабируемость** - легко добавлять новые типы локаций

## Поддержка

При возникновении проблем:

1. Проверьте консоль браузера на ошибки
2. Убедитесь, что файлы текстур существуют
3. Проверьте правильность путей в конфигурации
4. Используйте `window.testNewTextureSystem()` для диагностики

// Для TS: объявляем глобальную функцию для форс-рендера
declare global {
  interface Window {
    __forceLocationRerender?: () => void;
  }
}

/**
 * Новые утилиты для отрисовки интерьеров локаций в изометрическом стиле
 * Использует новую систему маппинга текстур
 */

import { WorldMap } from '../../../shared/types/World';
import { TerrainType, LocationDecorations } from '../../../shared/enums';
import { Location } from '../../../shared/types/Location';
import { isoToScreen, isTileVisible } from '../../utils/coordinates/isometric';
import { TILE_GAP, DECORATION_TEXTURE_SETTINGS, LOCATION_TERRAIN_TEXTURE_SETTINGS } from '../../utils/constants/renderingLocation';
import { textureLoader, TexturePathOptions } from './textureMapping';
import { getDeterministicVariation, extractCoordinatesFromSides, extractSideNumbers } from './utils';
import { PointWithNumber } from '../../../shared/types/Location';
import { drawSideTexturesNew } from './renderSideTextureLocInteriorNew';

// Константы для затемнения текстур
const DARKNESS_OPACITY = {
  MIN_VISIBILITY: 0.1, // Минимальная видимость в полной темноте
  CALCULATE_ALPHA: (darknessLevel: number) => Math.max(DARKNESS_OPACITY.MIN_VISIBILITY, 1 - darknessLevel)
} as const;

/**
 * Получает центр тайла на экране для локаций
 */
function getTileCenterOnScreen(isoX: number, isoY: number, tileWidth: number, tileHeight: number): [number, number] {
  const screenPos = isoToScreen(isoX, isoY, tileWidth, tileHeight);
  return [screenPos.x, screenPos.y];
}

/**
 * Новый менеджер текстур декораций локаций
 */
class NewLocationDecorationTextureManager {
  private loadingPromises = new Map<string, Promise<HTMLImageElement[]>>();

  /**
   * Получает текстуры для указанного типа декорации
   */
  async getDecorationTextures(
    decorationType: LocationDecorations,
    location?: Location,
    coordinates?: { x: number; y: number }
  ): Promise<HTMLImageElement[]> {
    const cacheKey = this.createCacheKey(decorationType, location, coordinates);

    // Проверяем, не загружается ли уже
    if (this.loadingPromises.has(cacheKey)) {
      return this.loadingPromises.get(cacheKey)!;
    }

    // Создаем промис загрузки
    const loadPromise = this.loadDecorationTextures(decorationType, location, coordinates);
    this.loadingPromises.set(cacheKey, loadPromise);

    try {
      const result = await loadPromise;
      this.loadingPromises.delete(cacheKey);
      return result;
    } catch (error) {
      this.loadingPromises.delete(cacheKey);
      console.warn(`Ошибка загрузки текстур декорации ${decorationType}:`, error);
      return [];
    }
  }

  /**
   * Загружает текстуры декорации
   */
  private async loadDecorationTextures(
    decorationType: LocationDecorations,
    location?: Location,
    coordinates?: { x: number; y: number }
  ): Promise<HTMLImageElement[]> {
    const options: TexturePathOptions = {
      decoration: decorationType,
      location,
      coordinates
    };

    try {
      const result = await textureLoader.loadDecorationTexture(decorationType, location, options);
      return [result.image];
    } catch (error) {
      console.warn(`Не удалось загрузить текстуру декорации ${decorationType}:`, error);
      return [];
    }
  }

  /**
   * Синхронно получает текстуры (если они уже загружены)
   */
  getLoadedTextures(
    decorationType: LocationDecorations,
    location?: Location,
    coordinates?: { x: number; y: number }
  ): HTMLImageElement[] | null {
    const options: TexturePathOptions = {
      decoration: decorationType,
      location,
      coordinates
    };

    const texture = textureLoader.getLoadedTexture(options);
    return texture ? [texture] : null;
  }

  /**
   * Предзагружает все текстуры декораций
   */
  async preloadAllTextures(): Promise<void> {
    const decorations = Object.values(LocationDecorations);
    const loadPromises = decorations.map(decoration =>
      textureLoader.preloadDecorationTextures(decoration)
    );

    await Promise.allSettled(loadPromises);
  }

  /**
   * Создает ключ кэша
   */
  private createCacheKey(
    decorationType: LocationDecorations,
    location?: Location,
    coordinates?: { x: number; y: number }
  ): string {
    const parts: string[] = [decorationType];

    if (location?.subtype) {
      parts.push(location.subtype);
    }

    if (location?.textureMaterial) {
      parts.push(location.textureMaterial);
    }

    if (coordinates) {
      parts.push(`${coordinates.x},${coordinates.y}`);
    }

    return parts.join('_');
  }
}

// Глобальный экземпляр нового менеджера текстур декораций локаций
export const newLocationDecorationTextureManager = new NewLocationDecorationTextureManager();

/**
 * Получает цвет местности для fallback отрисовки
 */
const getLocationTerrainColor = (terrain: TerrainType): string => {
  switch (terrain) {
    case TerrainType.ASPHALT:
      return '#555555'; // Серый для асфальта
    case TerrainType.BETON:
      return '#b0b0b0'; // Светло-серый для бетона
    case TerrainType.WOOD:
      return '#deb887'; // Бежевый для дерева
    case TerrainType.METAL:
      return '#a9a9a9'; // Металлический
    case TerrainType.GROUND:
      return '#8B4513'; // Коричневый для земли
    case TerrainType.WATER:
      return '#0000ff'; // Синий для воды
    case TerrainType.WASTELAND:
      return '#8B4513'; // Коричнево-серый для пустоши
    default:
      return '#8B4513'; // По умолчанию коричневый
  }
};

/**
 * Загружает текстуру местности
 */
async function loadLocationTerrainTexture(
  terrain: TerrainType,
  location?: Location,
  coordinates?: { x: number; y: number }
): Promise<HTMLImageElement | null> {
  try {
    const result = await textureLoader.loadTerrainTexture(terrain, location, { coordinates });
    return result.image;
  } catch (error) {
    console.warn(`Не удалось загрузить текстуру местности ${terrain}:`, error);
    return null;
  }
}

/**
 * Синхронно получает загруженную текстуру местности
 */
function getLoadedTerrainTexture(
  terrain: TerrainType,
  location?: Location,
  coordinates?: { x: number; y: number }
): HTMLImageElement | null {
  const options: TexturePathOptions = {
    terrain,
    location,
    coordinates
  };

  return textureLoader.getLoadedTexture(options);
}

/**
 * Отрисовывает текстуру декорации на ромбовидный тайл с новой системой
 */
const drawLocationDecorationNew = (
  ctx: CanvasRenderingContext2D,
  centerX: number,
  centerY: number,
  halfTileW: number,
  halfTileH: number,
  decorationType: LocationDecorations,
  isoX: number,
  isoY: number,
  rotation: number = 0,
  darknessLevel: number = 0,
  decorationSides?: PointWithNumber,
  locationData?: Location,
  playerPosition: { x: number; y: number } | null = null
): void => {
  // Если декорация VOID — не рисуем ничего
  if (decorationType === LocationDecorations.VOID) return;

  const coordinates = { x: isoX, y: isoY };

  // Обработка стен, окон и дверей с поддержкой decorationSides
  if (decorationType === LocationDecorations.WALL || 
      decorationType === LocationDecorations.WINDOW || 
      decorationType === LocationDecorations.DOOR ||
      decorationType === LocationDecorations.FENCE ||
      decorationType === LocationDecorations.MOUNTAINWALL ||
      decorationType === LocationDecorations.PARTITION
     ) {
    
    // Если есть decorationSides, используем новую логику
    if (decorationSides && decorationSides.length > 2) {
      drawSideTexturesNew(
        ctx,
        centerX,
        centerY,
        decorationType,
        decorationSides,
        locationData,
        undefined,
        darknessLevel,
        playerPosition,
        coordinates
      );
      return;
    }

    // Старая логика для декораций без decorationSides
    const textures = newLocationDecorationTextureManager.getLoadedTextures(
      decorationType, 
      locationData, 
      coordinates
    );
    if (!textures || textures.length === 0) {
      // Асинхронно загружаем текстуры
      newLocationDecorationTextureManager.getDecorationTextures(
        decorationType, 
        locationData, 
        coordinates
      );
      return; // Текстуры еще не загружены
    }

    // Выбираем текстуру детерминированно
    const textureIndex = getDeterministicVariation(isoX, isoY, textures.length) - 1;
    const texture = textures[textureIndex];

    if (!texture || !texture.complete) return;

    // Применяем настройки размера и смещения
    const scaleMultiplier = DECORATION_TEXTURE_SETTINGS.SCALE_MULTIPLIERS[decorationType.toUpperCase() as keyof typeof DECORATION_TEXTURE_SETTINGS.SCALE_MULTIPLIERS] || 1;
    const drawW = DECORATION_TEXTURE_SETTINGS.DEFAULT_WIDTH * scaleMultiplier;
    const drawH = DECORATION_TEXTURE_SETTINGS.DEFAULT_HEIGHT * scaleMultiplier;

    ctx.save();

    // Применяем затемнение
    if (darknessLevel > 0) {
      ctx.globalAlpha = DARKNESS_OPACITY.CALCULATE_ALPHA(darknessLevel);
    }

    // Применяем поворот
    if (rotation !== 0) {
      ctx.translate(centerX, centerY);
      ctx.rotate(rotation);
      ctx.translate(-centerX, -centerY);
    }

    // Вычисляем смещение если включено
    let offsetX = 0;
    let offsetY = 0;
    if (DECORATION_TEXTURE_SETTINGS.ENABLE_OFFSET) {
      offsetX = DECORATION_TEXTURE_SETTINGS.HORIZONTAL_OFFSET[decorationType.toUpperCase() as keyof typeof DECORATION_TEXTURE_SETTINGS.HORIZONTAL_OFFSET] || 0;
      offsetY = DECORATION_TEXTURE_SETTINGS.VERTICAL_OFFSET[decorationType.toUpperCase() as keyof typeof DECORATION_TEXTURE_SETTINGS.VERTICAL_OFFSET] || 0;
    }

    ctx.drawImage(texture, centerX - drawW / 2 + offsetX, centerY - drawH / 2 + offsetY, drawW, drawH);
    ctx.restore();
    return;
  }

  // Обычные декорации (не стены/окна/двери)
  const textures = newLocationDecorationTextureManager.getLoadedTextures(
    decorationType, 
    locationData, 
    coordinates
  );
  
  if (!textures || textures.length === 0) {
    // Асинхронно загружаем текстуры
    newLocationDecorationTextureManager.getDecorationTextures(
      decorationType, 
      locationData, 
      coordinates
    );
    return; // Текстуры еще не загружены
  }

  // Выбираем текстуру детерминированно
  const textureIndex = getDeterministicVariation(isoX, isoY, textures.length) - 1;
  const texture = textures[textureIndex];

  if (!texture || !texture.complete) return;

  // Применяем настройки размера и смещения
  const scaleMultiplier = DECORATION_TEXTURE_SETTINGS.SCALE_MULTIPLIERS[decorationType.toUpperCase() as keyof typeof DECORATION_TEXTURE_SETTINGS.SCALE_MULTIPLIERS] || 1;
  const drawW = DECORATION_TEXTURE_SETTINGS.DEFAULT_WIDTH * scaleMultiplier;
  const drawH = DECORATION_TEXTURE_SETTINGS.DEFAULT_HEIGHT * scaleMultiplier;

  ctx.save();

  // Применяем затемнение
  if (darknessLevel > 0) {
    ctx.globalAlpha = DARKNESS_OPACITY.CALCULATE_ALPHA(darknessLevel);
  }

  // Применяем поворот
  if (rotation !== 0) {
    ctx.translate(centerX, centerY);
    ctx.rotate(rotation);
    ctx.translate(-centerX, -centerY);
  }

  // Вычисляем смещение если включено
  let offsetX = 0;
  let offsetY = 0;
  if (DECORATION_TEXTURE_SETTINGS.ENABLE_OFFSET) {
    offsetX = DECORATION_TEXTURE_SETTINGS.HORIZONTAL_OFFSET[decorationType.toUpperCase() as keyof typeof DECORATION_TEXTURE_SETTINGS.HORIZONTAL_OFFSET] || 0;
    offsetY = DECORATION_TEXTURE_SETTINGS.VERTICAL_OFFSET[decorationType.toUpperCase() as keyof typeof DECORATION_TEXTURE_SETTINGS.VERTICAL_OFFSET] || 0;
  }

  ctx.drawImage(texture, centerX - drawW / 2 + offsetX, centerY - drawH / 2 + offsetY, drawW, drawH);
  ctx.restore();
};

/**
 * Отрисовывает декорации тайла локации с новой системой
 */
export const drawLocationTileDecorationsNew = (
  ctx: CanvasRenderingContext2D,
  isoX: number,
  isoY: number,
  tileWidth: number,
  tileHeight: number,
  location: Location | null,
  darknessLevel: number = 0,
  playerPosition: { x: number; y: number } | null = null
): void => {
  if (!location) return;

  const [centerX, centerY] = getTileCenterOnScreen(isoX, isoY, tileWidth, tileHeight);

  const halfTileW = tileWidth / 2 - TILE_GAP;
  const halfTileH = tileHeight / 2 - TILE_GAP;

  // Получаем данные тайла локации
  const locationTileKey = `${isoX},${isoY}`;
  const locationTile = location?.locationMap?.[locationTileKey];

  // Отрисовываем ТОЛЬКО декорации (исключаем VOID — это специальная пустая декорация)
  if (
    locationTile?.decoration &&
    locationTile.decoration !== LocationDecorations.NONE &&
    locationTile.decoration !== LocationDecorations.VOID
  ) {
    drawLocationDecorationNew(
      ctx,
      centerX,
      centerY,
      halfTileW,
      halfTileH,
      locationTile.decoration,
      isoX,
      isoY,
      0,
      darknessLevel,
      locationTile.decorationSides,
      location,
      playerPosition
    );
  }
};

/**
 * Отрисовывает тайл локации с новой системой
 */
export const drawLocationTileNew = (
  ctx: CanvasRenderingContext2D,
  isoX: number,
  isoY: number,
  tileWidth: number,
  tileHeight: number,
  location: Location | null,
  darknessLevel: number = 0,
  fillOpacity: number = 1.0
): void => {
  if (!location) return;

  const [centerX, centerY] = getTileCenterOnScreen(isoX, isoY, tileWidth, tileHeight);
  const halfTileW = tileWidth / 2 - TILE_GAP;
  const halfTileH = tileHeight / 2 - TILE_GAP;

  // Получаем данные тайла локации
  const locationTileKey = `${isoX},${isoY}`;
  const locationTile = location?.locationMap?.[locationTileKey];

  // Создаем путь для ромбовидного тайла
  ctx.beginPath();
  ctx.moveTo(centerX, centerY - halfTileH);
  ctx.lineTo(centerX + halfTileW, centerY);
  ctx.lineTo(centerX, centerY + halfTileH);
  ctx.lineTo(centerX - halfTileW, centerY);
  ctx.closePath();

  // Отрисовка тайла:
  let drewTexture = false;
  // Если есть текстура — рисуем её
  if (locationTile?.terrain) {
    const coordinates = { x: isoX, y: isoY };
    const cachedImg = getLoadedTerrainTexture(locationTile.terrain, location, coordinates);

    if (cachedImg && cachedImg.complete && cachedImg.width > 1) {
      ctx.save();
      ctx.clip();

      // Применяем итоговую alpha с учётом fillOpacity и darkness
      let finalAlpha = fillOpacity;
      if (darknessLevel > 0) {
        finalAlpha *= DARKNESS_OPACITY.CALCULATE_ALPHA(darknessLevel);
      }
      ctx.globalAlpha = finalAlpha;

      // Размеры текстуры
      const textureWidth = tileWidth * 1.2;
      const textureHeight = tileHeight * 1.2;

      // Поворот если включен
      let rotationAngle = 0;
      if (LOCATION_TERRAIN_TEXTURE_SETTINGS.ENABLE_ROTATION && locationTile.terrain) {
        const terrainKey = locationTile.terrain.toUpperCase() as keyof typeof LOCATION_TERRAIN_TEXTURE_SETTINGS.ROTATION_ANGLE;
        rotationAngle = LOCATION_TERRAIN_TEXTURE_SETTINGS.ROTATION_ANGLE[terrainKey] || 0;
      }

      // Вычисляем смещение если включено
      let offsetX = 0;
      let offsetY = 0;
      if (LOCATION_TERRAIN_TEXTURE_SETTINGS.ENABLE_OFFSET && locationTile.terrain) {
        const terrainKey = locationTile.terrain.toUpperCase() as keyof typeof LOCATION_TERRAIN_TEXTURE_SETTINGS.HORIZONTAL_OFFSET;
        offsetX = LOCATION_TERRAIN_TEXTURE_SETTINGS.HORIZONTAL_OFFSET[terrainKey] || 0;
        offsetY = LOCATION_TERRAIN_TEXTURE_SETTINGS.VERTICAL_OFFSET[terrainKey] || 0;
      }

      ctx.translate(centerX, centerY);
      ctx.rotate(rotationAngle);
      ctx.drawImage(
        cachedImg,
        -textureWidth / 2 + offsetX,
        -textureHeight / 2 + offsetY,
        textureWidth,
        textureHeight
      );
      ctx.restore();
      drewTexture = true;
    } else {
      // Асинхронно загружаем текстуру
      loadLocationTerrainTexture(locationTile.terrain, location, coordinates);
    }
  }

  // Если текстура не нарисована, рисуем цветную заливку
  if (!drewTexture) {
    ctx.save();

    // Применяем итоговую alpha с учётом fillOpacity и darkness
    let finalAlpha = fillOpacity;
    if (darknessLevel > 0) {
      finalAlpha *= DARKNESS_OPACITY.CALCULATE_ALPHA(darknessLevel);
    }
    ctx.globalAlpha = finalAlpha;

    const terrainColor = locationTile?.terrain
      ? getLocationTerrainColor(locationTile.terrain)
      : '#8B4513'; // По умолчанию коричневый

    ctx.fillStyle = terrainColor;
    ctx.fill();
    ctx.restore();
  }
};

/**
 * Предзагружает все текстуры локаций
 */
export const preloadAllLocationTexturesNew = async (): Promise<void> => {
  console.log('Начинается предзагрузка текстур локаций...');

  // Предзагружаем через новую систему
  await Promise.all([
    textureLoader.preloadAllBaseTextures(),
    newLocationDecorationTextureManager.preloadAllTextures()
  ]);

  console.log('Предзагрузка текстур локаций завершена');
};

/**
 * Получает статистику загрузки текстур
 */
export const getTextureLoadingStatsNew = () => {
  return textureLoader.getLoadingStats();
};

/**
 * Очищает кэш текстур
 */
export const clearTexturesCacheNew = (): void => {
  textureLoader.clearCache();
};

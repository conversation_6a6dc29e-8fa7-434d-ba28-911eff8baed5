# Конфигурация текстур для изометрических локаций

Этот файл объясняет, как легко добавлять новые текстуры без изменения кода.

## Структура папок

Все текстуры должны быть организованы в следующей структуре:

```
/textures/Location/
├── decorations/
│   ├── [decoration_name]/
│   │   ├── 1.png, 2.png, 3.png, 4.png (базовые варианты)
│   │   ├── [material]/
│   │   │   ├── 1.png, 2.png, 3.png, 4.png
│   │   │   └── [combo_key]/
│   │   │       ├── 1.png, 2.png, 3.png, 4.png
│   │   │       ├── open/ (только для дверей)
│   │   │       │   └── 1.png, 2.png, 3.png, 4.png
│   │   │       └── closed/ (только для дверей)
│   │   │           └── 1.png, 2.png, 3.png, 4.png
│   │   └── [subtype]/
│   │       └── [decoration_name]/
│   │           └── 1.png, 2.png, 3.png, 4.png
└── terrain/
    ├── [terrain_name]/
    │   ├── 1.png, 2.png, 3.png, 4.png (базовые варианты)
    │   └── [subtype]/
    │       └── [terrain_name]/
    │           └── 1.png, 2.png, 3.png, 4.png
```

## Примеры путей

### Базовые декорации
- `/textures/Location/decorations/wall/1.png`
- `/textures/Location/decorations/wall/2.png`
- `/textures/Location/decorations/rocks/1.png`

### Декорации с материалами
- `/textures/Location/decorations/wall/brick/1.png`
- `/textures/Location/decorations/window/wood/1.png`
- `/textures/Location/decorations/door/metal/1.png`

### Декорации с decorationSides (стены, окна, двери)
- `/textures/Location/decorations/wall/brick/1_3/1.png` (север-юг)
- `/textures/Location/decorations/wall/brick/2_4/1.png` (восток-запад)
- `/textures/Location/decorations/wall/brick/1_2_3_4/1.png` (все стороны)

### Двери с состояниями
- `/textures/Location/decorations/door/wood/1_3/open/1.png`
- `/textures/Location/decorations/door/wood/1_3/closed/1.png`

### Декорации для подтипов локаций
- `/textures/Location/decorations/subway/wall/1.png`
- `/textures/Location/decorations/subway/sign/1.png`
- `/textures/Location/decorations/hospital/furniture/1.png`

### Местность
- `/textures/Location/terrain/beton/1.png`
- `/textures/Location/terrain/subway/beton/1.png`

## Как добавить новую декорацию

1. **Добавьте enum в shared/enums/index.ts:**
   ```typescript
   export enum LocationDecorations {
     // ... существующие
     MY_NEW_DECORATION = 'my_new_decoration'
   }
   ```

2. **Создайте папку с текстурами:**
   ```
   /textures/Location/decorations/my_new_decoration/
   ├── 1.png
   ├── 2.png
   ├── 3.png
   └── 4.png
   ```

3. **Добавьте конфигурацию в textureConfigs.ts:**
   ```typescript
   [LocationDecorations.MY_NEW_DECORATION]: {
     basePath: '/textures/Location/decorations/my_new_decoration',
     variations: 4
   }
   ```

4. **Готово!** Система автоматически подхватит новую декорацию.

## Как добавить поддержку нового подтипа локации

1. **Создайте папку для подтипа:**
   ```
   /textures/Location/decorations/my_subtype/
   └── wall/
       ├── 1.png
       ├── 2.png
       └── 3.png
   ```

2. **Добавьте в конфигурацию:**
   ```typescript
   [LocationDecorations.WALL]: {
     basePath: '/textures/Location/decorations/wall',
     variations: 4,
     subtypeVariants: {
       [LocationSubtype.MY_SUBTYPE]: {
         basePath: '/textures/Location/decorations/my_subtype/wall',
         variations: 3
       }
     }
   }
   ```

## Как добавить поддержку нового материала

1. **Создайте папку для материала:**
   ```
   /textures/Location/decorations/wall/my_material/
   ├── 1.png
   ├── 2.png
   └── 3.png
   ```

2. **Добавьте в конфигурацию:**
   ```typescript
   [LocationDecorations.WALL]: {
     basePath: '/textures/Location/decorations/wall',
     variations: 4,
     materialVariants: {
       [MaterialTexture.MY_MATERIAL]: {
         basePath: '/textures/Location/decorations/wall/my_material',
         variations: 3
       }
     }
   }
   ```

## Комбинации сторон (decorationSides)

Для стен, окон, дверей и заборов поддерживаются следующие комбинации:

- `1` - север
- `2` - восток  
- `3` - юг
- `4` - запад
- `1_2` - северо-восток (угол)
- `1_3` - север-юг (прямая линия)
- `2_4` - восток-запад (прямая линия)
- `1_2_3` - север-восток-юг (тройка)
- `1_2_3_4` - все стороны

## Fallback система

Если текстура не найдена, система автоматически:

1. Попробует найти текстуру без учета подтипа
2. Попробует найти текстуру без учета материала
3. Попробует найти базовую текстуру
4. Покажет фиолетовый круг как последний fallback

## Советы

- Всегда нумеруйте файлы с 1.png (не с 0.png)
- Используйте одинаковые размеры для всех вариаций одной декорации
- Для лучшей производительности используйте PNG с оптимизацией
- Тестируйте новые текстуры в разных условиях освещения

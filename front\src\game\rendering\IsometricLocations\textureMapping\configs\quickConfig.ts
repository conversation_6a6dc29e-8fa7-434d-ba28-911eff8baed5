/**
 * Быстрая конфигурация для добавления новых текстур
 * Этот файл можно редактировать без знания TypeScript
 */

import { LocationDecorations, LocationSubtype, MaterialTexture, TerrainType } from '../../../../../shared/enums';

/**
 * ПРОСТОЕ ДОБАВЛЕНИЕ НОВЫХ ДЕКОРАЦИЙ
 * 
 * Формат: 'decoration_name': количество_вариаций
 * 
 * Пример:
 * 'rocks': 8 означает что есть файлы rocks/1.png, rocks/2.png, ..., rocks/8.png
 */
export const SIMPLE_DECORATIONS = {
  // Природные элементы
  'rocks': 8,
  'tree': 8,
  'bush': 8,
  'grass': 8,
  'log': 8,
  
  // Контейнеры
  'box': 8,
  'barrel': 4,
  'tire': 8,
  
  // Мусор и отходы
  'litter': 4,
  
  // Жидкости
  'water': 4,
  'puddle': 8,
  'mud': 8,
  
  // Дороги
  'road': 8,
  
  // Транспорт
  'car': 4,
  
  // Освещение
  'streetlight': 4,
  
  // Знаки
  'sign': 8,
  'poster': 8
};

/**
 * ДЕКОРАЦИИ С ПОДДЕРЖКОЙ МАТЕРИАЛОВ
 * 
 * Формат: 'decoration_name': { variations: число, materials: ['material1', 'material2'] }
 */
export const MATERIAL_DECORATIONS = {
  'wall': {
    variations: 4,
    materials: ['brick', 'beton', 'wood', 'metal'],
    supportsSides: true
  },
  'window': {
    variations: 4,
    materials: ['brick', 'beton', 'wood', 'metal'],
    supportsSides: true
  },
  'door': {
    variations: 4,
    materials: ['brick', 'beton', 'wood', 'metal'],
    supportsSides: true,
    supportsOpenClose: true
  },
  'fence': {
    variations: 4,
    materials: ['wood', 'metal'],
    supportsSides: true
  }
};

/**
 * ДЕКОРАЦИИ ДЛЯ СПЕЦИАЛЬНЫХ ПОДТИПОВ ЛОКАЦИЙ
 * 
 * Формат: 'subtype': { 'decoration': variations }
 */
export const SUBTYPE_DECORATIONS = {
  'subway': {
    'wall': 4,
    'window': 2,
    'door': 2,
    'sign': 6,
    'poster': 12,
    'furniture': 3,
    'turnstile': 3,
    'rocks': 4,
    'tree': 2
  },
  'hospital': {
    'furniture': 6,
    'sign': 4
  },
  'school': {
    'furniture': 5,
    'sign': 3
  },
  'military': {
    'furniture': 4,
    'sign': 5
  },
  'police': {
    'furniture': 4,
    'sign': 4
  }
};

/**
 * ТИПЫ МЕСТНОСТИ
 * 
 * Формат: 'terrain_name': количество_вариаций
 */
export const SIMPLE_TERRAINS = {
  'asphalt': 4,
  'beton': 6,
  'wood': 4,
  'metal': 4,
  'ground': 6,
  'water': 4,
  'wasteland': 8
};

/**
 * МЕСТНОСТЬ ДЛЯ ПОДТИПОВ ЛОКАЦИЙ
 * 
 * Формат: 'subtype': { 'terrain': variations }
 */
export const SUBTYPE_TERRAINS = {
  'subway': {
    'beton': 8
  }
};

/**
 * АВТОМАТИЧЕСКАЯ ГЕНЕРАЦИЯ КОНФИГУРАЦИИ
 * Эта функция автоматически создает полную конфигурацию из простых настроек выше
 */
export function generateTextureConfig() {
  const config: any = {};

  // Простые декорации
  Object.entries(SIMPLE_DECORATIONS).forEach(([name, variations]) => {
    config[name] = {
      basePath: `/textures/Location/decorations/${name}`,
      variations
    };
  });

  // Декорации с материалами
  Object.entries(MATERIAL_DECORATIONS).forEach(([name, settings]) => {
    const materialVariants: any = {};
    
    settings.materials.forEach(material => {
      materialVariants[material] = {
        basePath: `/textures/Location/decorations/${name}/${material}`,
        variations: settings.variations
      };
    });

    config[name] = {
      basePath: `/textures/Location/decorations/${name}`,
      variations: settings.variations,
      materialVariants,
      supportsSides: settings.supportsSides || false,
      supportsOpenClose: settings.supportsOpenClose || false
    };
  });

  // Добавляем подтипы к существующим декорациям
  Object.entries(SUBTYPE_DECORATIONS).forEach(([subtype, decorations]) => {
    Object.entries(decorations).forEach(([decoration, variations]) => {
      if (config[decoration]) {
        if (!config[decoration].subtypeVariants) {
          config[decoration].subtypeVariants = {};
        }
        config[decoration].subtypeVariants[subtype] = {
          basePath: `/textures/Location/decorations/${subtype}/${decoration}`,
          variations
        };
      }
    });
  });

  // Простая местность
  Object.entries(SIMPLE_TERRAINS).forEach(([name, variations]) => {
    config[name] = {
      basePath: `/textures/Location/terrain/${name}`,
      variations
    };
  });

  // Добавляем подтипы к местности
  Object.entries(SUBTYPE_TERRAINS).forEach(([subtype, terrains]) => {
    Object.entries(terrains).forEach(([terrain, variations]) => {
      if (config[terrain]) {
        if (!config[terrain].subtypeVariants) {
          config[terrain].subtypeVariants = {};
        }
        config[terrain].subtypeVariants[subtype] = {
          basePath: `/textures/Location/terrain/${subtype}/${terrain}`,
          variations
        };
      }
    });
  });

  return config;
}

/**
 * ИНСТРУКЦИИ ДЛЯ ДОБАВЛЕНИЯ НОВЫХ ТЕКСТУР:
 * 
 * 1. Для простой декорации:
 *    - Добавьте 'my_decoration': 4 в SIMPLE_DECORATIONS
 *    - Создайте папку /textures/Location/decorations/my_decoration/
 *    - Добавьте файлы 1.png, 2.png, 3.png, 4.png
 * 
 * 2. Для декорации с материалами:
 *    - Добавьте в MATERIAL_DECORATIONS:
 *      'my_decoration': {
 *        variations: 4,
 *        materials: ['wood', 'metal'],
 *        supportsSides: true  // если поддерживает decorationSides
 *      }
 *    - Создайте папки:
 *      /textures/Location/decorations/my_decoration/wood/1.png...
 *      /textures/Location/decorations/my_decoration/metal/1.png...
 * 
 * 3. Для декорации подтипа:
 *    - Добавьте в SUBTYPE_DECORATIONS:
 *      'my_subtype': {
 *        'wall': 3,
 *        'furniture': 5
 *      }
 *    - Создайте папки:
 *      /textures/Location/decorations/my_subtype/wall/1.png...
 *      /textures/Location/decorations/my_subtype/furniture/1.png...
 * 
 * 4. Для новой местности:
 *    - Добавьте 'my_terrain': 6 в SIMPLE_TERRAINS
 *    - Создайте папку /textures/Location/terrain/my_terrain/
 *    - Добавьте файлы 1.png, 2.png, ..., 6.png
 * 
 * ВАЖНО: После изменения этого файла перезапустите приложение!
 */

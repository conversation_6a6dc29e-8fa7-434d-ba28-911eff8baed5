/**
 * Пример использования новой системы маппинга текстур
 * Этот файл демонстрирует, как использовать новую систему
 */

import { LocationDecorations, LocationSubtype, MaterialTexture, TerrainType } from '../../../shared/enums';
import { Location } from '../../../shared/types/Location';
import { textureLoader, textureMapper } from './textureMapping';

/**
 * Пример 1: Загрузка простой декорации
 */
async function loadSimpleDecoration() {
  console.log('=== Пример 1: Простая декорация ===');
  
  try {
    const result = await textureLoader.loadDecorationTexture(LocationDecorations.ROCKS);
    console.log('Загружена текстура камней:', result.loadedPath);
    console.log('Это fallback?', result.isFallback);
  } catch (error) {
    console.error('Ошибка загрузки:', error);
  }
}

/**
 * Пример 2: Загрузка декорации с материалом
 */
async function loadDecorationWithMaterial() {
  console.log('=== Пример 2: Декорация с материалом ===');
  
  const location: Partial<Location> = {
    textureMaterial: MaterialTexture.BRICK
  };
  
  try {
    const result = await textureLoader.loadDecorationTexture(
      LocationDecorations.WALL, 
      location as Location
    );
    console.log('Загружена текстура стены из кирпича:', result.loadedPath);
  } catch (error) {
    console.error('Ошибка загрузки:', error);
  }
}

/**
 * Пример 3: Загрузка декорации для подтипа локации
 */
async function loadDecorationForSubtype() {
  console.log('=== Пример 3: Декорация для подтипа локации ===');
  
  const subwayLocation: Partial<Location> = {
    subtype: LocationSubtype.SUBWAY,
    textureMaterial: MaterialTexture.BETON
  };
  
  try {
    const result = await textureLoader.loadDecorationTexture(
      LocationDecorations.SIGN, 
      subwayLocation as Location
    );
    console.log('Загружена текстура знака для метро:', result.loadedPath);
  } catch (error) {
    console.error('Ошибка загрузки:', error);
  }
}

/**
 * Пример 4: Загрузка текстуры с decorationSides
 */
async function loadSideTexture() {
  console.log('=== Пример 4: Текстура с decorationSides ===');
  
  const location: Partial<Location> = {
    textureMaterial: MaterialTexture.WOOD
  };
  
  const decorationSides = [5, 10, [1, 3]]; // x=5, y=10, стороны север-юг
  
  try {
    const result = await textureLoader.loadSideTexture(
      LocationDecorations.DOOR,
      decorationSides,
      location as Location,
      { isOpen: false }
    );
    console.log('Загружена текстура закрытой двери:', result.loadedPath);
  } catch (error) {
    console.error('Ошибка загрузки:', error);
  }
}

/**
 * Пример 5: Загрузка текстуры местности
 */
async function loadTerrainTexture() {
  console.log('=== Пример 5: Текстура местности ===');
  
  const location: Partial<Location> = {
    subtype: LocationSubtype.SUBWAY
  };
  
  try {
    const result = await textureLoader.loadTerrainTexture(
      TerrainType.BETON,
      location as Location,
      { coordinates: { x: 3, y: 7 } }
    );
    console.log('Загружена текстура бетона для метро:', result.loadedPath);
  } catch (error) {
    console.error('Ошибка загрузки:', error);
  }
}

/**
 * Пример 6: Получение путей без загрузки
 */
function getTexturePaths() {
  console.log('=== Пример 6: Получение путей к текстурам ===');
  
  const location: Partial<Location> = {
    subtype: LocationSubtype.HOSPITAL,
    textureMaterial: MaterialTexture.METAL
  };
  
  const paths = textureMapper.getTexturePath({
    decoration: LocationDecorations.FURNITURE,
    location: location as Location,
    coordinates: { x: 2, y: 4 }
  });
  
  console.log('Пути к текстурам мебели для больницы:');
  paths.forEach((path, index) => {
    console.log(`  ${index + 1}. ${path}`);
  });
}

/**
 * Пример 7: Предзагрузка текстур
 */
async function preloadTextures() {
  console.log('=== Пример 7: Предзагрузка текстур ===');
  
  console.log('Начинается предзагрузка...');
  const startTime = Date.now();
  
  try {
    await textureLoader.preloadAllBaseTextures();
    const endTime = Date.now();
    console.log(`Предзагрузка завершена за ${endTime - startTime}мс`);
    
    const stats = textureLoader.getLoadingStats();
    console.log('Статистика загрузки:', stats);
  } catch (error) {
    console.error('Ошибка предзагрузки:', error);
  }
}

/**
 * Пример 8: Работа с fallback системой
 */
async function testFallbackSystem() {
  console.log('=== Пример 8: Fallback система ===');
  
  try {
    // Пытаемся загрузить несуществующую декорацию
    const result = await textureLoader.loadDecorationTexture(
      'nonexistent_decoration' as LocationDecorations
    );
    console.log('Fallback текстура загружена:', result.loadedPath);
    console.log('Это fallback?', result.isFallback);
  } catch (error) {
    console.error('Ошибка fallback:', error);
  }
}

/**
 * Запуск всех примеров
 */
export async function runExamples() {
  console.log('🚀 Запуск примеров новой системы маппинга текстур');
  console.log('================================================');
  
  await loadSimpleDecoration();
  console.log('');
  
  await loadDecorationWithMaterial();
  console.log('');
  
  await loadDecorationForSubtype();
  console.log('');
  
  await loadSideTexture();
  console.log('');
  
  await loadTerrainTexture();
  console.log('');
  
  getTexturePaths();
  console.log('');
  
  await testFallbackSystem();
  console.log('');
  
  await preloadTextures();
  console.log('');
  
  console.log('✅ Все примеры выполнены!');
  console.log('================================================');
}

/**
 * Функция для тестирования в браузере
 * Вызовите window.testNewTextureSystem() в консоли браузера
 */
if (typeof window !== 'undefined') {
  (window as any).testNewTextureSystem = runExamples;
  console.log('💡 Для тестирования новой системы текстур вызовите: window.testNewTextureSystem()');
}

/**
 * Утилиты для работы с decorationSides
 */

/**
 * Создает ключ комбинации для decorationSides
 */
export function buildComboKey(sideNumbers: number[]): string {
  if (!sideNumbers || sideNumbers.length === 0) {
    return '';
  }

  let comboKey = sideNumbers.slice().sort((a, b) => a - b).join('_');
  
  // Нормализация для одиночных направлений
  if (sideNumbers.length === 1) {
    const only = sideNumbers[0];
    if (only === 1 || only === 3) comboKey = '1_3';
    else if (only === 2 || only === 4) comboKey = '2_4';
  }
  
  return comboKey;
}

/**
 * Проверяет, валидны ли данные decorationSides
 */
export function isValidDecorationSides(decorationSides: any): boolean {
  return (
    decorationSides &&
    Array.isArray(decorationSides) &&
    decorationSides.length > 2 &&
    Array.isArray(decorationSides[2]) &&
    decorationSides[2].length > 0
  );
}

/**
 * Извлекает координаты из decorationSides
 */
export function extractCoordinatesFromSides(decorationSides: number[]): { x: number; y: number } | null {
  if (!isValidDecorationSides(decorationSides)) {
    return null;
  }

  return {
    x: Number(decorationSides[0]) || 0,
    y: Number(decorationSides[1]) || 0
  };
}

/**
 * Извлекает номера сторон из decorationSides
 */
export function extractSideNumbers(decorationSides: number[]): number[] {
  if (!isValidDecorationSides(decorationSides)) {
    return [];
  }

  return decorationSides[2] as number[];
}

/**
 * Создает полные данные decorationSides
 */
export function createDecorationSides(x: number, y: number, sideNumbers: number[]): number[] {
  return [x, y, sideNumbers];
}

/**
 * Проверяет, содержит ли decorationSides определенную сторону
 */
export function hasSide(decorationSides: number[], side: number): boolean {
  const sideNumbers = extractSideNumbers(decorationSides);
  return sideNumbers.includes(side);
}

/**
 * Получает все возможные комбинации сторон
 */
export function getAllSideCombinations(): string[] {
  const combinations = [
    '1', '2', '3', '4',           // одиночные стороны
    '1_2', '1_3', '1_4',          // пары с 1
    '2_3', '2_4',                 // пары с 2
    '3_4',                        // пара 3-4
    '1_2_3', '1_2_4', '1_3_4',    // тройки
    '2_3_4',                      // тройка 2-3-4
    '1_2_3_4'                     // все стороны
  ];

  return combinations;
}

/**
 * Нормализует комбинацию сторон
 */
export function normalizeSideCombination(sideNumbers: number[]): number[] {
  // Удаляем дубликаты и сортируем
  const unique = [...new Set(sideNumbers)].sort((a, b) => a - b);
  
  // Фильтруем только валидные стороны (1-4)
  return unique.filter(side => side >= 1 && side <= 4);
}

/**
 * Проверяет, является ли комбинация сторон угловой
 */
export function isCornerCombination(sideNumbers: number[]): boolean {
  const normalized = normalizeSideCombination(sideNumbers);
  
  // Угловые комбинации: смежные стороны
  const corners = [
    [1, 2], [2, 3], [3, 4], [4, 1]
  ];
  
  if (normalized.length !== 2) {
    return false;
  }
  
  return corners.some(corner => 
    corner[0] === normalized[0] && corner[1] === normalized[1]
  );
}

/**
 * Проверяет, является ли комбинация сторон прямой линией
 */
export function isStraightCombination(sideNumbers: number[]): boolean {
  const normalized = normalizeSideCombination(sideNumbers);
  
  // Прямые линии: противоположные стороны
  const straights = [
    [1, 3], [2, 4]
  ];
  
  if (normalized.length !== 2) {
    return false;
  }
  
  return straights.some(straight => 
    straight[0] === normalized[0] && straight[1] === normalized[1]
  );
}

/**
 * Получает тип комбинации сторон
 */
export function getSideCombinationType(sideNumbers: number[]): 'single' | 'corner' | 'straight' | 'triple' | 'quad' | 'unknown' {
  const normalized = normalizeSideCombination(sideNumbers);
  
  switch (normalized.length) {
    case 1:
      return 'single';
    case 2:
      if (isCornerCombination(normalized)) return 'corner';
      if (isStraightCombination(normalized)) return 'straight';
      return 'unknown';
    case 3:
      return 'triple';
    case 4:
      return 'quad';
    default:
      return 'unknown';
  }
}

/**
 * Получает описание комбинации сторон для отладки
 */
export function describeSideCombination(sideNumbers: number[]): string {
  const normalized = normalizeSideCombination(sideNumbers);
  const type = getSideCombinationType(normalized);
  const combo = buildComboKey(normalized);
  
  return `${type}(${combo})`;
}

/**
 * Проверяет совместимость двух комбинаций сторон
 */
export function areCompatibleCombinations(sides1: number[], sides2: number[]): boolean {
  const norm1 = normalizeSideCombination(sides1);
  const norm2 = normalizeSideCombination(sides2);
  
  // Проверяем, есть ли пересечения
  return norm1.some(side => norm2.includes(side));
}

/**
 * Объединяет две комбинации сторон
 */
export function mergeSideCombinations(sides1: number[], sides2: number[]): number[] {
  const combined = [...sides1, ...sides2];
  return normalizeSideCombination(combined);
}

/**
 * Получает противоположные стороны
 */
export function getOppositeSides(sideNumbers: number[]): number[] {
  const opposites: Record<number, number> = {
    1: 3, 2: 4, 3: 1, 4: 2
  };
  
  return sideNumbers.map(side => opposites[side]).filter(side => side !== undefined);
}

/**
 * Получает смежные стороны
 */
export function getAdjacentSides(side: number): number[] {
  const adjacents: Record<number, number[]> = {
    1: [2, 4],
    2: [1, 3],
    3: [2, 4],
    4: [1, 3]
  };
  
  return adjacents[side] || [];
}

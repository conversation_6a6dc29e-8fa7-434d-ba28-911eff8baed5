/**
 * Утилиты для работы с координатами и детерминированным выбором вариаций
 */

/**
 * Получает детерминированную вариацию текстуры на основе координат
 * Использует математические константы для лучшего распределения
 */
export function getDeterministicVariation(
  x: number, 
  y: number, 
  maxVariations: number,
  seed: number = 0
): number {
  // Детерминированный выбор с использованием числа π для лучшего распределения
  const p = (x * Math.PI + y * Math.PI * 2.71828 + seed * 0.618);
  const hash = Math.abs(Math.sin(p) * Math.cos(p * 1.618) * 10000);
  const index = Math.floor(hash) % maxVariations;
  return index + 1; // файлы нумеруются с 1
}

/**
 * Получает детерминированную вариацию для декораций с другой формулой
 * для большего разнообразия
 */
export function getDeterministicDecorationVariation(
  x: number, 
  y: number, 
  maxVariations: number,
  seed: number = 0
): number {
  // Детерминированный выбор с другой формулой на основе π для разнообразия декораций
  const p = (x * Math.PI * 1.414 + y * Math.PI * 1.732 + seed * 0.618);
  const hash = Math.abs(Math.sin(p * 2.236) * Math.cos(p / 1.618) * 50000); // Умножаем на 50000 для еще большего разнообразия
  const index = Math.floor(hash) % maxVariations;
  return index + 1;
}

/**
 * Создает seed на основе строки (например, comboKey)
 */
export function createSeedFromString(str: string): number {
  return str.split('').reduce((acc, char, index) => {
    return acc + char.charCodeAt(0) * (index + 1);
  }, 0);
}

/**
 * Создает seed на основе массива чисел
 */
export function createSeedFromNumbers(numbers: number[]): number {
  return numbers.reduce((acc, num, index) => {
    return acc + num * (index + 1);
  }, 0);
}

/**
 * Нормализует координаты для стабильного хеширования
 */
export function normalizeCoordinates(x: number, y: number): { x: number; y: number } {
  return {
    x: Math.floor(x),
    y: Math.floor(y)
  };
}

/**
 * Создает уникальный ключ на основе координат и дополнительных параметров
 */
export function createCoordinateKey(
  x: number, 
  y: number, 
  ...additionalParams: (string | number)[]
): string {
  const normalized = normalizeCoordinates(x, y);
  const params = additionalParams.length > 0 ? `_${additionalParams.join('_')}` : '';
  return `${normalized.x},${normalized.y}${params}`;
}

/**
 * Проверяет, находятся ли координаты в заданном диапазоне
 */
export function isInRange(
  x: number, 
  y: number, 
  minX: number, 
  minY: number, 
  maxX: number, 
  maxY: number
): boolean {
  return x >= minX && x <= maxX && y >= minY && y <= maxY;
}

/**
 * Вычисляет расстояние между двумя точками
 */
export function getDistance(
  x1: number, 
  y1: number, 
  x2: number, 
  y2: number
): number {
  const dx = x2 - x1;
  const dy = y2 - y1;
  return Math.sqrt(dx * dx + dy * dy);
}

/**
 * Получает соседние координаты (8 направлений)
 */
export function getNeighborCoordinates(x: number, y: number): Array<{ x: number; y: number }> {
  return [
    { x: x - 1, y: y - 1 }, // северо-запад
    { x: x, y: y - 1 },     // север
    { x: x + 1, y: y - 1 }, // северо-восток
    { x: x - 1, y: y },     // запад
    { x: x + 1, y: y },     // восток
    { x: x - 1, y: y + 1 }, // юго-запад
    { x: x, y: y + 1 },     // юг
    { x: x + 1, y: y + 1 }  // юго-восток
  ];
}

/**
 * Получает координаты в радиусе от центральной точки
 */
export function getCoordinatesInRadius(
  centerX: number, 
  centerY: number, 
  radius: number
): Array<{ x: number; y: number }> {
  const coordinates: Array<{ x: number; y: number }> = [];
  
  for (let x = centerX - radius; x <= centerX + radius; x++) {
    for (let y = centerY - radius; y <= centerY + radius; y++) {
      if (getDistance(centerX, centerY, x, y) <= radius) {
        coordinates.push({ x, y });
      }
    }
  }
  
  return coordinates;
}

/**
 * Интерполирует между двумя значениями
 */
export function lerp(start: number, end: number, factor: number): number {
  return start + (end - start) * factor;
}

/**
 * Ограничивает значение в заданном диапазоне
 */
export function clamp(value: number, min: number, max: number): number {
  return Math.min(Math.max(value, min), max);
}

/**
 * Преобразует координаты из одной системы в другую
 */
export function transformCoordinates(
  x: number, 
  y: number, 
  offsetX: number = 0, 
  offsetY: number = 0, 
  scaleX: number = 1, 
  scaleY: number = 1
): { x: number; y: number } {
  return {
    x: (x + offsetX) * scaleX,
    y: (y + offsetY) * scaleY
  };
}

/**
 * Новая система загрузки текстур с поддержкой маппинга и fallback
 */

import { LocationDecorations, TerrainType } from '../../../../shared/enums';
import { Location } from '../../../../shared/types/Location';
import { TexturePathOptions, TextureLoadResult } from './types';
import { textureMapper } from './TextureMapper';
import { createFallbackTexture, isValidTexture } from './utils/fallbackUtils';
import { globalTextureCache } from '../utils/textureCache';

export class TextureLoader {
  private static instance: TextureLoader;
  private loadTimeout: number = 10000; // 10 секунд таймаут
  private maxRetries: number = 3;

  private constructor() {}

  public static getInstance(): TextureLoader {
    if (!TextureLoader.instance) {
      TextureLoader.instance = new TextureLoader();
    }
    return TextureLoader.instance;
  }

  /**
   * Загружает текстуру декорации
   */
  async loadDecorationTexture(
    decoration: LocationDecorations,
    location?: Location,
    options?: Partial<TexturePathOptions>
  ): Promise<TextureLoadResult> {
    const pathOptions: TexturePathOptions = {
      decoration,
      location,
      material: location?.textureMaterial,
      ...options
    };

    return this.loadTextureWithOptions(pathOptions);
  }

  /**
   * Загружает текстуру местности
   */
  async loadTerrainTexture(
    terrain: TerrainType,
    location?: Location,
    options?: Partial<TexturePathOptions>
  ): Promise<TextureLoadResult> {
    const pathOptions: TexturePathOptions = {
      terrain,
      location,
      ...options
    };

    return this.loadTextureWithOptions(pathOptions);
  }

  /**
   * Загружает текстуру с учетом decorationSides
   */
  async loadSideTexture(
    decoration: LocationDecorations,
    decorationSides: number[],
    location?: Location,
    options?: Partial<TexturePathOptions>
  ): Promise<TextureLoadResult> {
    const pathOptions: TexturePathOptions = {
      decoration,
      decorationSides,
      location,
      material: location?.textureMaterial,
      ...options
    };

    return this.loadTextureWithOptions(pathOptions);
  }

  /**
   * Основной метод загрузки текстуры
   */
  private async loadTextureWithOptions(options: TexturePathOptions): Promise<TextureLoadResult> {
    try {
      // Получаем пути к текстурам
      const paths = textureMapper.getTexturePath(options);
      
      if (paths.length === 0) {
        return this.createFallbackResult('No paths found');
      }

      // Пробуем загрузить по порядку приоритета
      for (const path of paths) {
        try {
          const result = await this.loadSingleTexture(path);
          if (result.success) {
            return {
              image: result.image,
              loadedPath: path,
              isFallback: path.includes('fallback')
            };
          }
        } catch (error) {
          console.warn(`Не удалось загрузить текстуру: ${path}`, error);
          continue;
        }
      }

      // Если ничего не загрузилось, возвращаем fallback
      return this.createFallbackResult('All paths failed');

    } catch (error) {
      console.error('Ошибка при загрузке текстуры:', error);
      return this.createFallbackResult('Loading error');
    }
  }

  /**
   * Загружает одну текстуру с кэшированием
   */
  private async loadSingleTexture(path: string): Promise<{ success: boolean; image: HTMLImageElement }> {
    // Проверяем кэш
    const cached = globalTextureCache.get(path);
    if (cached && isValidTexture(cached)) {
      return { success: true, image: cached };
    }

    // Проверяем, не загружается ли уже
    const loadingPromise = globalTextureCache.getLoadingPromise(path);
    if (loadingPromise) {
      try {
        const image = await loadingPromise;
        return { success: isValidTexture(image), image };
      } catch (error) {
        return { success: false, image: createFallbackTexture() };
      }
    }

    // Создаем новый промис загрузки
    const loadPromise = this.createLoadPromise(path);
    globalTextureCache.setLoadingPromise(path, loadPromise);

    try {
      const image = await loadPromise;
      globalTextureCache.deleteLoadingPromise(path);
      
      if (isValidTexture(image)) {
        globalTextureCache.set(path, image);
        return { success: true, image };
      } else {
        return { success: false, image: createFallbackTexture() };
      }
    } catch (error) {
      globalTextureCache.deleteLoadingPromise(path);
      throw error;
    }
  }

  /**
   * Создает промис загрузки изображения с таймаутом и повторами
   */
  private createLoadPromise(path: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      let retries = 0;

      const attemptLoad = () => {
        const img = new Image();
        const timeoutId = setTimeout(() => {
          img.src = ''; // Прерываем загрузку
          if (retries < this.maxRetries) {
            retries++;
            console.warn(`Таймаут загрузки ${path}, попытка ${retries}/${this.maxRetries}`);
            attemptLoad();
          } else {
            reject(new Error(`Timeout loading texture: ${path}`));
          }
        }, this.loadTimeout);

        img.onload = () => {
          clearTimeout(timeoutId);
          resolve(img);
        };

        img.onerror = () => {
          clearTimeout(timeoutId);
          if (retries < this.maxRetries) {
            retries++;
            console.warn(`Ошибка загрузки ${path}, попытка ${retries}/${this.maxRetries}`);
            attemptLoad();
          } else {
            reject(new Error(`Failed to load texture: ${path}`));
          }
        };

        img.src = path;
      };

      attemptLoad();
    });
  }

  /**
   * Создает fallback результат
   */
  private createFallbackResult(reason: string): TextureLoadResult {
    console.warn(`Используется fallback текстура: ${reason}`);
    return {
      image: createFallbackTexture(),
      loadedPath: '/textures/fallback/purple_circle.png',
      isFallback: true
    };
  }

  /**
   * Синхронно получает загруженную текстуру
   */
  getLoadedTexture(options: TexturePathOptions): HTMLImageElement | null {
    const paths = textureMapper.getTexturePath(options);
    
    for (const path of paths) {
      const cached = globalTextureCache.get(path);
      if (cached && isValidTexture(cached)) {
        return cached;
      }
    }

    return null;
  }

  /**
   * Предзагружает текстуры для декорации
   */
  async preloadDecorationTextures(decoration: LocationDecorations): Promise<void> {
    // Предзагружаем базовые варианты
    const baseOptions: TexturePathOptions = { decoration };
    await this.loadTextureWithOptions(baseOptions);

    // Предзагружаем варианты с материалами
    const materials = ['brick', 'beton', 'wood', 'metal'];
    for (const material of materials) {
      const options: TexturePathOptions = { 
        decoration, 
        material: material as any 
      };
      await this.loadTextureWithOptions(options).catch(() => {
        // Игнорируем ошибки при предзагрузке
      });
    }
  }

  /**
   * Предзагружает текстуры для местности
   */
  async preloadTerrainTextures(terrain: TerrainType): Promise<void> {
    const options: TexturePathOptions = { terrain };
    await this.loadTextureWithOptions(options);
  }

  /**
   * Предзагружает все базовые текстуры
   */
  async preloadAllBaseTextures(): Promise<void> {
    console.log('Начинается предзагрузка базовых текстур...');
    
    // Предзагружаем декорации
    const decorations = Object.values(LocationDecorations);
    const decorationPromises = decorations.map(decoration => 
      this.preloadDecorationTextures(decoration).catch(() => {})
    );

    // Предзагружаем местность
    const terrains = Object.values(TerrainType);
    const terrainPromises = terrains.map(terrain => 
      this.preloadTerrainTextures(terrain).catch(() => {})
    );

    await Promise.allSettled([...decorationPromises, ...terrainPromises]);
    console.log('Предзагрузка базовых текстур завершена');
  }

  /**
   * Получает статистику загрузки
   */
  getLoadingStats() {
    const cacheStats = globalTextureCache.getStats();
    const loadingInfo = globalTextureCache.getLoadingInfo();
    
    return {
      ...cacheStats,
      ...loadingInfo,
      cacheSize: globalTextureCache.size()
    };
  }

  /**
   * Очищает кэш текстур
   */
  clearCache(): void {
    globalTextureCache.clear();
  }

  /**
   * Настраивает параметры загрузки
   */
  configure(options: { timeout?: number; maxRetries?: number }): void {
    if (options.timeout !== undefined) {
      this.loadTimeout = options.timeout;
    }
    if (options.maxRetries !== undefined) {
      this.maxRetries = options.maxRetries;
    }
  }
}

// Экспортируем singleton instance
export const textureLoader = TextureLoader.getInstance();

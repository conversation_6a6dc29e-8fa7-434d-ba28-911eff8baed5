/**
 * Утилиты для создания fallback текстур
 */

import { FallbackOptions } from '../types';

/**
 * Создает fallback текстуру (фиолетовый круг)
 */
export function createFallbackTexture(options?: Partial<FallbackOptions>): HTMLImageElement {
  const opts: FallbackOptions = {
    backgroundColor: '#8B00FF', // Фиолетовый цвет
    size: { width: 64, height: 64 },
    text: '?',
    textColor: '#FFFFFF',
    ...options
  };

  const canvas = document.createElement('canvas');
  canvas.width = opts.size.width;
  canvas.height = opts.size.height;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) {
    // Если не удалось создать контекст, возвращаем пустое изображение
    const img = new Image(1, 1);
    return img;
  }

  // Рисуем фиолетовый круг
  const centerX = opts.size.width / 2;
  const centerY = opts.size.height / 2;
  const radius = Math.min(opts.size.width, opts.size.height) / 2 - 2;

  ctx.fillStyle = opts.backgroundColor;
  ctx.beginPath();
  ctx.arc(centerX, centerY, radius, 0, 2 * Math.PI);
  ctx.fill();

  // Добавляем текст, если указан
  if (opts.text) {
    ctx.fillStyle = opts.textColor!;
    ctx.font = `bold ${Math.floor(radius)}px Arial`;
    ctx.textAlign = 'center';
    ctx.textBaseline = 'middle';
    ctx.fillText(opts.text, centerX, centerY);
  }

  // Создаем изображение из canvas
  const img = new Image();
  img.src = canvas.toDataURL();
  
  return img;
}

/**
 * Создает fallback текстуру с указанным цветом
 */
export function createColoredFallback(color: string, size?: { width: number; height: number }): HTMLImageElement {
  return createFallbackTexture({
    backgroundColor: color,
    size: size || { width: 64, height: 64 },
    text: undefined
  });
}

/**
 * Создает fallback текстуру с текстом
 */
export function createTextFallback(text: string, options?: Partial<FallbackOptions>): HTMLImageElement {
  return createFallbackTexture({
    ...options,
    text
  });
}

/**
 * Проверяет, является ли изображение валидным (не пустым)
 */
export function isValidTexture(img: HTMLImageElement): boolean {
  return img.complete && img.width > 1 && img.height > 1;
}

/**
 * Создает прозрачную fallback текстуру
 */
export function createTransparentFallback(size?: { width: number; height: number }): HTMLImageElement {
  const opts = {
    width: size?.width || 64,
    height: size?.height || 64
  };

  const canvas = document.createElement('canvas');
  canvas.width = opts.width;
  canvas.height = opts.height;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) {
    const img = new Image(1, 1);
    return img;
  }

  // Создаем полностью прозрачное изображение
  ctx.clearRect(0, 0, opts.width, opts.height);

  const img = new Image();
  img.src = canvas.toDataURL();
  
  return img;
}

/**
 * Создает fallback текстуру с градиентом
 */
export function createGradientFallback(
  colors: string[], 
  size?: { width: number; height: number }
): HTMLImageElement {
  const opts = {
    width: size?.width || 64,
    height: size?.height || 64
  };

  const canvas = document.createElement('canvas');
  canvas.width = opts.width;
  canvas.height = opts.height;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) {
    const img = new Image(1, 1);
    return img;
  }

  // Создаем радиальный градиент
  const centerX = opts.width / 2;
  const centerY = opts.height / 2;
  const radius = Math.min(opts.width, opts.height) / 2;

  const gradient = ctx.createRadialGradient(centerX, centerY, 0, centerX, centerY, radius);
  
  colors.forEach((color, index) => {
    gradient.addColorStop(index / (colors.length - 1), color);
  });

  ctx.fillStyle = gradient;
  ctx.fillRect(0, 0, opts.width, opts.height);

  const img = new Image();
  img.src = canvas.toDataURL();
  
  return img;
}

/**
 * Создает fallback текстуру с шахматным узором
 */
export function createCheckerboardFallback(
  color1: string = '#FF00FF', 
  color2: string = '#800080',
  size?: { width: number; height: number },
  checkerSize: number = 8
): HTMLImageElement {
  const opts = {
    width: size?.width || 64,
    height: size?.height || 64
  };

  const canvas = document.createElement('canvas');
  canvas.width = opts.width;
  canvas.height = opts.height;
  
  const ctx = canvas.getContext('2d');
  if (!ctx) {
    const img = new Image(1, 1);
    return img;
  }

  // Рисуем шахматный узор
  for (let x = 0; x < opts.width; x += checkerSize) {
    for (let y = 0; y < opts.height; y += checkerSize) {
      const isEven = (Math.floor(x / checkerSize) + Math.floor(y / checkerSize)) % 2 === 0;
      ctx.fillStyle = isEven ? color1 : color2;
      ctx.fillRect(x, y, checkerSize, checkerSize);
    }
  }

  const img = new Image();
  img.src = canvas.toDataURL();
  
  return img;
}

/**
 * Кэш для fallback текстур
 */
const fallbackCache = new Map<string, HTMLImageElement>();

/**
 * Получает кэшированную fallback текстуру или создает новую
 */
export function getCachedFallback(key: string, factory: () => HTMLImageElement): HTMLImageElement {
  if (fallbackCache.has(key)) {
    return fallbackCache.get(key)!;
  }

  const fallback = factory();
  fallbackCache.set(key, fallback);
  return fallback;
}

/**
 * Очищает кэш fallback текстур
 */
export function clearFallbackCache(): void {
  fallbackCache.clear();
}

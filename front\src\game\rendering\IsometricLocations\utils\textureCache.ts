/**
 * Утилиты для работы с кэшем текстур
 */

import { TextureCache, TextureStats } from '../textureMapping/types';

/**
 * Менеджер кэша текстур
 */
export class TextureCacheManager {
  private cache: TextureCache;
  private maxCacheSize: number;
  private cleanupThreshold: number;

  constructor(maxCacheSize: number = 1000, cleanupThreshold: number = 0.8) {
    this.cache = {
      images: new Map(),
      loadingPromises: new Map(),
      lastUsed: new Map()
    };
    this.maxCacheSize = maxCacheSize;
    this.cleanupThreshold = cleanupThreshold;
  }

  /**
   * Получает изображение из кэша
   */
  get(path: string): HTMLImageElement | undefined {
    const image = this.cache.images.get(path);
    if (image) {
      this.cache.lastUsed.set(path, Date.now());
    }
    return image;
  }

  /**
   * Сохраняет изображение в кэш
   */
  set(path: string, image: HTMLImageElement): void {
    // Проверяем, нужна ли очистка кэша
    if (this.cache.images.size >= this.maxCacheSize * this.cleanupThreshold) {
      this.cleanup();
    }

    this.cache.images.set(path, image);
    this.cache.lastUsed.set(path, Date.now());
  }

  /**
   * Проверяет, есть ли изображение в кэше
   */
  has(path: string): boolean {
    return this.cache.images.has(path);
  }

  /**
   * Удаляет изображение из кэша
   */
  delete(path: string): boolean {
    this.cache.lastUsed.delete(path);
    return this.cache.images.delete(path);
  }

  /**
   * Получает промис загрузки
   */
  getLoadingPromise(path: string): Promise<HTMLImageElement> | undefined {
    return this.cache.loadingPromises.get(path);
  }

  /**
   * Сохраняет промис загрузки
   */
  setLoadingPromise(path: string, promise: Promise<HTMLImageElement>): void {
    this.cache.loadingPromises.set(path, promise);
  }

  /**
   * Удаляет промис загрузки
   */
  deleteLoadingPromise(path: string): boolean {
    return this.cache.loadingPromises.delete(path);
  }

  /**
   * Очищает старые записи из кэша
   */
  cleanup(): void {
    const now = Date.now();
    const maxAge = 5 * 60 * 1000; // 5 минут
    const targetSize = Math.floor(this.maxCacheSize * 0.7); // Удаляем до 70% от максимума

    // Собираем записи с временем последнего использования
    const entries = Array.from(this.cache.lastUsed.entries())
      .map(([path, lastUsed]) => ({ path, lastUsed, age: now - lastUsed }))
      .sort((a, b) => b.age - a.age); // Сортируем по возрасту (старые первыми)

    // Удаляем старые записи
    let removed = 0;
    for (const entry of entries) {
      if (this.cache.images.size <= targetSize) {
        break;
      }

      // Удаляем если запись старая или нужно освободить место
      if (entry.age > maxAge || removed < entries.length - targetSize) {
        this.delete(entry.path);
        removed++;
      }
    }

    console.log(`Очищен кэш текстур: удалено ${removed} записей, осталось ${this.cache.images.size}`);
  }

  /**
   * Полностью очищает кэш
   */
  clear(): void {
    this.cache.images.clear();
    this.cache.loadingPromises.clear();
    this.cache.lastUsed.clear();
  }

  /**
   * Получает статистику кэша
   */
  getStats(): TextureStats {
    const totalLoaded = this.cache.images.size;
    let successfulLoads = 0;
    let fallbackUsed = 0;
    let cacheSize = 0;

    for (const [path, image] of this.cache.images) {
      if (image.complete && image.width > 1) {
        successfulLoads++;
        // Приблизительный расчет размера
        cacheSize += image.width * image.height * 4; // 4 байта на пиксель (RGBA)
      }
      
      if (path.includes('fallback') || path.includes('purple_circle')) {
        fallbackUsed++;
      }
    }

    return {
      totalLoaded,
      successfulLoads,
      fallbackUsed,
      cacheSize
    };
  }

  /**
   * Получает размер кэша
   */
  size(): number {
    return this.cache.images.size;
  }

  /**
   * Получает все пути в кэше
   */
  getPaths(): string[] {
    return Array.from(this.cache.images.keys());
  }

  /**
   * Проверяет валидность изображения
   */
  isValidImage(image: HTMLImageElement): boolean {
    return image.complete && image.width > 1 && image.height > 1;
  }

  /**
   * Предзагружает список текстур
   */
  async preload(paths: string[], loadFunction: (path: string) => Promise<HTMLImageElement>): Promise<void> {
    const loadPromises = paths.map(async (path) => {
      if (this.has(path)) {
        return; // Уже в кэше
      }

      try {
        const image = await loadFunction(path);
        this.set(path, image);
      } catch (error) {
        console.warn(`Не удалось предзагрузить текстуру: ${path}`, error);
      }
    });

    await Promise.allSettled(loadPromises);
  }

  /**
   * Получает информацию о загрузке
   */
  getLoadingInfo(): { loading: number; cached: number; total: number } {
    return {
      loading: this.cache.loadingPromises.size,
      cached: this.cache.images.size,
      total: this.cache.images.size + this.cache.loadingPromises.size
    };
  }

  /**
   * Проверяет, загружается ли текстура
   */
  isLoading(path: string): boolean {
    return this.cache.loadingPromises.has(path);
  }

  /**
   * Ждет завершения загрузки всех текстур
   */
  async waitForAllLoading(): Promise<void> {
    const promises = Array.from(this.cache.loadingPromises.values());
    await Promise.allSettled(promises);
  }

  /**
   * Получает наиболее используемые текстуры
   */
  getMostUsed(limit: number = 10): Array<{ path: string; lastUsed: number }> {
    return Array.from(this.cache.lastUsed.entries())
      .map(([path, lastUsed]) => ({ path, lastUsed }))
      .sort((a, b) => b.lastUsed - a.lastUsed)
      .slice(0, limit);
  }

  /**
   * Получает наименее используемые текстуры
   */
  getLeastUsed(limit: number = 10): Array<{ path: string; lastUsed: number }> {
    return Array.from(this.cache.lastUsed.entries())
      .map(([path, lastUsed]) => ({ path, lastUsed }))
      .sort((a, b) => a.lastUsed - b.lastUsed)
      .slice(0, limit);
  }

  /**
   * Экспортирует кэш для отладки
   */
  exportCache(): any {
    return {
      images: Array.from(this.cache.images.keys()),
      loading: Array.from(this.cache.loadingPromises.keys()),
      lastUsed: Array.from(this.cache.lastUsed.entries()),
      stats: this.getStats()
    };
  }
}

/**
 * Глобальный экземпляр менеджера кэша
 */
export const globalTextureCache = new TextureCacheManager();

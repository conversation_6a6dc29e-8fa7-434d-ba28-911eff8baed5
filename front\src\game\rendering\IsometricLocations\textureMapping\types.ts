/**
 * Типы для системы маппинга текстур
 */

import { LocationDecorations, LocationSubtype, MaterialTexture, TerrainType } from '../../../../shared/enums';
import { Location } from '../../../../shared/types/Location';

/**
 * Опции для получения пути к текстуре
 */
export interface TexturePathOptions {
  /** Тип декорации (если применимо) */
  decoration?: LocationDecorations;
  
  /** Тип местности (если применимо) */
  terrain?: TerrainType;
  
  /** Данные локации */
  location?: Location;
  
  /** Материал текстуры */
  material?: MaterialTexture;
  
  /** Данные о сторонах декорации [x, y, number[]] */
  decorationSides?: number[];
  
  /** Координаты для детерминированного выбора вариации */
  coordinates?: { x: number; y: number };
  
  /** Состояние открыто/закрыто (для дверей) */
  isOpen?: boolean;
}

/**
 * Конфигурация текстуры для конкретного элемента
 */
export interface TextureConfig {
  /** Базовый путь к текстурам */
  basePath: string;
  
  /** Количество вариаций */
  variations?: number;
  
  /** Варианты для разных подтипов локаций */
  subtypeVariants?: Partial<Record<LocationSubtype, SubtypeTextureConfig>>;
  
  /** Варианты для разных материалов */
  materialVariants?: Partial<Record<MaterialTexture, MaterialTextureConfig>>;
  
  /** Поддерживает ли decorationSides */
  supportsSides?: boolean;
  
  /** Поддерживает ли состояния открыто/закрыто */
  supportsOpenClose?: boolean;
}

/**
 * Конфигурация текстуры для подтипа локации
 */
export interface SubtypeTextureConfig {
  /** Путь к текстурам для этого подтипа */
  basePath: string;
  
  /** Количество вариаций */
  variations?: number;
  
  /** Приоритет (чем выше, тем раньше проверяется) */
  priority?: number;
}

/**
 * Конфигурация текстуры для материала
 */
export interface MaterialTextureConfig {
  /** Путь к текстурам для этого материала */
  basePath: string;
  
  /** Количество вариаций */
  variations?: number;
  
  /** Приоритет (чем выше, тем раньше проверяется) */
  priority?: number;
}

/**
 * Fallback текстура
 */
export interface FallbackTexture {
  /** Путь к fallback текстуре */
  path: string;
  
  /** Цвет для создания программной заглушки */
  color?: string;
  
  /** Размер заглушки */
  size?: { width: number; height: number };
}

/**
 * Результат загрузки текстуры
 */
export interface TextureLoadResult {
  /** Загруженное изображение */
  image: HTMLImageElement;
  
  /** Путь, по которому была загружена текстура */
  loadedPath: string;
  
  /** Была ли использована fallback текстура */
  isFallback: boolean;
}

/**
 * Кэш текстур
 */
export interface TextureCache {
  /** Кэшированные изображения */
  images: Map<string, HTMLImageElement>;
  
  /** Промисы загрузки */
  loadingPromises: Map<string, Promise<HTMLImageElement>>;
  
  /** Время последнего использования */
  lastUsed: Map<string, number>;
}

/**
 * Статистика использования текстур
 */
export interface TextureStats {
  /** Общее количество загруженных текстур */
  totalLoaded: number;
  
  /** Количество успешно загруженных */
  successfulLoads: number;
  
  /** Количество fallback текстур */
  fallbackUsed: number;
  
  /** Размер кэша в байтах (приблизительно) */
  cacheSize: number;
}

/**
 * Конфигурация для предзагрузки
 */
export interface PreloadConfig {
  /** Предзагружать ли все текстуры сразу */
  preloadAll: boolean;
  
  /** Предзагружать ли только базовые текстуры */
  preloadBaseOnly: boolean;
  
  /** Максимальное количество одновременных загрузок */
  maxConcurrentLoads: number;
  
  /** Таймаут для загрузки одной текстуры (мс) */
  loadTimeout: number;
}

/**
 * Опции для создания fallback текстуры
 */
export interface FallbackOptions {
  /** Цвет фона */
  backgroundColor: string;
  
  /** Размер */
  size: { width: number; height: number };
  
  /** Текст для отображения */
  text?: string;
  
  /** Цвет текста */
  textColor?: string;
}

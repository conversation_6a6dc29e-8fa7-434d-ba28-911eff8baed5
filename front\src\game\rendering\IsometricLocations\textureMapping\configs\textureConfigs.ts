/**
 * Конфигурации текстур для всех элементов локаций
 * Этот файл автоматически генерируется из quickConfig.ts
 * Для добавления новых текстур редактируйте quickConfig.ts
 */

import { TextureConfig } from '../types';
import { generateTextureConfig } from './quickConfig';

// Генерируем конфигурацию автоматически из quickConfig.ts
const generatedConfig = generateTextureConfig();

// Все конфигурации теперь генерируются автоматически из quickConfig.ts
// Для добавления новых текстур редактируйте файл quickConfig.ts

/**
 * Объединенные конфигурации для всех типов элементов
 * Автоматически генерируется из quickConfig.ts
 */
export const textureConfigs: Record<string, TextureConfig> = {
  ...generatedConfig,
  // Здесь можно добавить ручные переопределения если нужно
  // ...manualOverrides
} as Record<string, TextureConfig>;

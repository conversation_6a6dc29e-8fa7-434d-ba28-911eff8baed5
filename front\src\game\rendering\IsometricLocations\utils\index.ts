/**
 * Экспорты утилит для изометрических локаций
 */

// Утилиты для координат
export {
  getDeterministicVariation,
  getDeterministicDecorationVariation,
  createSeedFromString,
  createSeedFromNumbers,
  normalizeCoordinates,
  createCoordinateKey,
  isInRange,
  getDistance,
  getNeighborCoordinates,
  getCoordinatesInRadius,
  lerp,
  clamp,
  transformCoordinates
} from './coordinateUtils';

// Утилиты для decorationSides
export {
  buildComboKey,
  isValidDecorationSides,
  extractCoordinatesFromSides,
  extractSideNumbers,
  createDecorationSides,
  hasSide,
  getAllSideCombinations,
  normalizeSideCombination,
  isCornerCombination,
  isStraightCombination,
  getSideCombinationType,
  describeSideCombination,
  areCompatibleCombinations,
  mergeSideCombinations,
  getOppositeSides,
  getAdjacentSides
} from './decorationSidesUtils';

// Утилиты для кэша текстур
export {
  TextureCacheManager,
  globalTextureCache
} from './textureCache';

/**
 * Централизованная система маппинга текстур для изометрических локаций
 * Поддерживает subType локаций, материалы и fallback систему
 */

import { LocationDecorations, LocationSubtype, MaterialTexture, TerrainType } from '../../../../shared/enums';
import { Location } from '../../../../shared/types/Location';
import { TextureConfig, TexturePathOptions, FallbackTexture } from './types';
import { textureConfigs } from './configs/textureConfigs';
import { createFallbackTexture } from './utils/fallbackUtils';

export class TextureMapper {
  private static instance: TextureMapper;
  private textureCache = new Map<string, HTMLImageElement>();
  private loadingPromises = new Map<string, Promise<HTMLImageElement>>();

  private constructor() {}

  public static getInstance(): TextureMapper {
    if (!TextureMapper.instance) {
      TextureMapper.instance = new TextureMapper();
    }
    return TextureMapper.instance;
  }

  /**
   * Получает путь к текстуре с учетом всех параметров локации
   */
  public getTexturePath(options: TexturePathOptions): string[] {
    const {
      decoration,
      terrain,
      location,
      material,
      decorationSides,
      coordinates,
      isOpen
    } = options;

    // Определяем тип элемента для поиска конфигурации
    const elementType = decoration || terrain;
    if (!elementType) {
      return this.getFallbackPaths();
    }

    const config = textureConfigs[elementType];
    if (!config) {
      return this.getFallbackPaths();
    }

    // Строим пути с учетом приоритетов
    const paths: string[] = [];
    
    // 1. Пути с учетом subType локации
    if (location?.subtype && config.subtypeVariants?.[location.subtype]) {
      paths.push(...this.buildPathsForSubtype(config, location.subtype, options));
    }

    // 2. Пути с учетом материала
    if (material && config.materialVariants?.[material]) {
      paths.push(...this.buildPathsForMaterial(config, material, options));
    }

    // 3. Базовые пути
    paths.push(...this.buildBasePaths(config, options));

    // 4. Fallback пути
    paths.push(...this.getFallbackPaths());

    return paths;
  }

  /**
   * Строит пути для конкретного subType локации
   */
  private buildPathsForSubtype(
    config: TextureConfig,
    subtype: LocationSubtype,
    options: TexturePathOptions
  ): string[] {
    const subtypeConfig = config.subtypeVariants![subtype];
    const basePath = subtypeConfig.basePath;
    
    return this.buildVariationPaths(basePath, subtypeConfig, options);
  }

  /**
   * Строит пути для конкретного материала
   */
  private buildPathsForMaterial(
    config: TextureConfig,
    material: MaterialTexture,
    options: TexturePathOptions
  ): string[] {
    const materialConfig = config.materialVariants![material];
    const basePath = materialConfig.basePath;
    
    return this.buildVariationPaths(basePath, materialConfig, options);
  }

  /**
   * Строит базовые пути
   */
  private buildBasePaths(config: TextureConfig, options: TexturePathOptions): string[] {
    return this.buildVariationPaths(config.basePath, config, options);
  }

  /**
   * Строит пути с учетом вариаций
   */
  private buildVariationPaths(
    basePath: string,
    config: TextureConfig | any,
    options: TexturePathOptions
  ): string[] {
    const paths: string[] = [];
    const { decorationSides, coordinates, isOpen, decoration } = options;

    // Для decorationSides (стены, окна, двери)
    if (decorationSides && decorationSides.length > 2) {
      const sideNumbers = decorationSides[2] as number[];
      if (sideNumbers && sideNumbers.length > 0) {
        const comboKey = this.buildComboKey(sideNumbers);
        
        // Для дверей добавляем состояние открыто/закрыто
        if (decoration === LocationDecorations.DOOR && typeof isOpen === 'boolean') {
          const stateFolder = isOpen ? 'open' : 'closed';
          const variation = this.getVariation(coordinates, comboKey, config.variations || 4);
          paths.push(`${basePath}/${comboKey}/${stateFolder}/${variation}.png`);
        }
        
        // Обычный путь без состояния
        const variation = this.getVariation(coordinates, comboKey, config.variations || 4);
        paths.push(`${basePath}/${comboKey}/${variation}.png`);
      }
    } else {
      // Обычные декорации без sides
      const variation = this.getVariation(coordinates, '', config.variations || 4);
      paths.push(`${basePath}/${variation}.png`);
    }

    return paths;
  }

  /**
   * Создает ключ комбинации для decorationSides
   */
  private buildComboKey(sideNumbers: number[]): string {
    let comboKey = sideNumbers.slice().sort((a, b) => a - b).join('_');
    
    // Нормализация для одиночных направлений
    if (sideNumbers.length === 1) {
      const only = sideNumbers[0];
      if (only === 1 || only === 3) comboKey = '1_3';
      else if (only === 2 || only === 4) comboKey = '2_4';
    }
    
    return comboKey;
  }

  /**
   * Получает детерминированную вариацию
   */
  private getVariation(
    coordinates: { x: number; y: number } | undefined,
    comboKey: string,
    maxVariations: number
  ): number {
    if (!coordinates) {
      return 1;
    }

    const { x, y } = coordinates;
    const seed = comboKey.split('_').reduce((acc, v) => acc + Number(v || 0), 0);
    const p = (x * Math.PI * 1.414 + y * Math.PI * 1.732 + seed * 0.618);
    const hash = Math.abs(Math.sin(p * 2.236) * Math.cos(p / 1.618) * 10000);
    const index = Math.floor(hash) % maxVariations;
    return index + 1;
  }

  /**
   * Получает fallback пути
   */
  private getFallbackPaths(): string[] {
    return ['/textures/fallback/purple_circle.png'];
  }

  /**
   * Загружает текстуру по списку путей (пробует по порядку)
   */
  public async loadTexture(paths: string[]): Promise<HTMLImageElement> {
    for (const path of paths) {
      try {
        // Проверяем кэш
        if (this.textureCache.has(path)) {
          const cached = this.textureCache.get(path)!;
          if (cached.complete && cached.width > 1) {
            return cached;
          }
        }

        // Проверяем, не загружается ли уже
        if (this.loadingPromises.has(path)) {
          const result = await this.loadingPromises.get(path)!;
          if (result.complete && result.width > 1) {
            return result;
          }
          continue;
        }

        // Загружаем новую текстуру
        const loadPromise = this.loadSingleTexture(path);
        this.loadingPromises.set(path, loadPromise);
        
        const result = await loadPromise;
        this.loadingPromises.delete(path);
        
        if (result.complete && result.width > 1) {
          return result;
        }
      } catch (error) {
        console.warn(`Не удалось загрузить текстуру: ${path}`);
        continue;
      }
    }

    // Если ничего не загрузилось, создаем fallback
    return createFallbackTexture();
  }

  /**
   * Загружает одну текстуру
   */
  private loadSingleTexture(src: string): Promise<HTMLImageElement> {
    return new Promise((resolve, reject) => {
      const img = new Image();
      img.onload = () => {
        this.textureCache.set(src, img);
        resolve(img);
      };
      img.onerror = () => {
        reject(new Error(`Failed to load texture: ${src}`));
      };
      img.src = src;
    });
  }

  /**
   * Синхронно получает загруженную текстуру
   */
  public getLoadedTexture(paths: string[]): HTMLImageElement | null {
    for (const path of paths) {
      const cached = this.textureCache.get(path);
      if (cached && cached.complete && cached.width > 1) {
        return cached;
      }
    }
    return null;
  }

  /**
   * Предзагружает текстуры для всех конфигураций
   */
  public async preloadAllTextures(): Promise<void> {
    const allPaths = new Set<string>();
    
    // Собираем все возможные пути из конфигураций
    Object.values(textureConfigs).forEach(config => {
      this.collectPathsFromConfig(config, allPaths);
    });

    // Загружаем все уникальные пути
    const loadPromises = Array.from(allPaths).map(path => 
      this.loadSingleTexture(path).catch(() => null)
    );

    await Promise.allSettled(loadPromises);
  }

  /**
   * Собирает все пути из конфигурации
   */
  private collectPathsFromConfig(config: TextureConfig, pathSet: Set<string>): void {
    // Базовые пути
    for (let i = 1; i <= (config.variations || 4); i++) {
      pathSet.add(`${config.basePath}/${i}.png`);
    }

    // Пути для subType
    if (config.subtypeVariants) {
      Object.values(config.subtypeVariants).forEach(subtypeConfig => {
        for (let i = 1; i <= (subtypeConfig.variations || 4); i++) {
          pathSet.add(`${subtypeConfig.basePath}/${i}.png`);
        }
      });
    }

    // Пути для материалов
    if (config.materialVariants) {
      Object.values(config.materialVariants).forEach(materialConfig => {
        for (let i = 1; i <= (materialConfig.variations || 4); i++) {
          pathSet.add(`${materialConfig.basePath}/${i}.png`);
        }
      });
    }
  }
}

// Экспортируем singleton instance
export const textureMapper = TextureMapper.getInstance();
